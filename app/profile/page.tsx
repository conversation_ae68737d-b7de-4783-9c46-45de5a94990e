/**
 * Profile Page for Disc Golf Inventory Management System
 *
 * This page provides centralized user management including user preferences,
 * collection summary, and account settings.
 *
 * Requirements Satisfied:
 * - FR-PROFILE-001: Display user preferences, collection summary, and account settings
 * - FR-PROFILE-002: Persist changes and update relevant application areas
 */

"use client";

import * as React from "react";
import { Layout, PageContainer, Section } from "@/components/layout/Layout";
import { useProfile } from "@/hooks/useProfile";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Lazy load profile components for better performance
const ProfileHeader = React.lazy(() =>
  import("@/components/profile/ProfileHeader").then((mod) => ({ default: mod.ProfileHeader }))
);
const ProfilePreferences = React.lazy(() =>
  import("@/components/profile/ProfilePreferences").then((mod) => ({ default: mod.ProfilePreferences }))
);
const CollectionSummary = React.lazy(() =>
  import("@/components/profile/CollectionSummary").then((mod) => ({ default: mod.CollectionSummary }))
);
const AccountSettings = React.lazy(() =>
  import("@/components/profile/AccountSettings").then((mod) => ({ default: mod.AccountSettings }))
);

// ============================================================================
// LOADING COMPONENT
// ============================================================================

/**
 * Loading component for profile sections
 */
function ProfileLoading() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="bg-card rounded-lg border p-6">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-muted rounded-full animate-pulse" />
          <div className="space-y-2">
            <div className="w-32 h-6 bg-muted rounded animate-pulse" />
            <div className="w-48 h-4 bg-muted rounded animate-pulse" />
          </div>
        </div>
      </div>

      {/* Content skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-card rounded-lg border p-6">
          <div className="w-40 h-6 bg-muted rounded animate-pulse mb-4" />
          <div className="space-y-3">
            <div className="w-full h-4 bg-muted rounded animate-pulse" />
            <div className="w-3/4 h-4 bg-muted rounded animate-pulse" />
            <div className="w-1/2 h-4 bg-muted rounded animate-pulse" />
          </div>
        </div>
        <div className="bg-card rounded-lg border p-6">
          <div className="w-40 h-6 bg-muted rounded animate-pulse mb-4" />
          <div className="space-y-3">
            <div className="w-full h-4 bg-muted rounded animate-pulse" />
            <div className="w-3/4 h-4 bg-muted rounded animate-pulse" />
            <div className="w-1/2 h-4 bg-muted rounded animate-pulse" />
          </div>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// MAIN PROFILE PAGE COMPONENT
// ============================================================================

/**
 * Profile page content component
 */
function ProfilePageContent() {
  const {
    profile,
    collectionSummary,
    loading,
    saving,
    error,
    updatePersonalInfo,
    updateCollectionPreferences,
    updateAppSettings,
    resetProfile,
  } = useProfile();

  // Show loading state
  if (loading) {
    return (
      <Layout>
        <PageContainer title="Profile" description="Manage your account and preferences">
          <ProfileLoading />
        </PageContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer title="Profile" description="Manage your account and preferences">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Profile Header Section */}
          <Section>
            <React.Suspense fallback={<ProfileLoading />}>
              <ProfileHeader
                personalInfo={profile.personalInfo}
                collectionSummary={collectionSummary}
                onUpdatePersonalInfo={updatePersonalInfo}
                saving={saving}
              />
            </React.Suspense>
          </Section>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-8">
              {/* User Preferences Section */}
              <Section title="Preferences" description="Customize your disc golf inventory experience">
                <React.Suspense fallback={<ProfileLoading />}>
                  <ProfilePreferences
                    collectionPreferences={profile.collectionPreferences}
                    appSettings={profile.appSettings}
                    onUpdateCollectionPreferences={updateCollectionPreferences}
                    onUpdateAppSettings={updateAppSettings}
                    saving={saving}
                  />
                </React.Suspense>
              </Section>
            </div>

            {/* Right Column */}
            <div className="space-y-8">
              {/* Collection Summary Section */}
              <Section title="Collection Summary" description="Overview of your disc golf collection">
                <React.Suspense fallback={<ProfileLoading />}>
                  <CollectionSummary
                    collectionSummary={collectionSummary}
                    showPurchasePrices={profile.collectionPreferences.showPurchasePrices}
                  />
                </React.Suspense>
              </Section>

              {/* Account Settings Section */}
              <Section title="Account Settings" description="Manage your account and data">
                <React.Suspense fallback={<ProfileLoading />}>
                  <AccountSettings profile={profile} onResetProfile={resetProfile} saving={saving} />
                </React.Suspense>
              </Section>
            </div>
          </div>
        </div>
      </PageContainer>
    </Layout>
  );
}

// ============================================================================
// MAIN EXPORT
// ============================================================================

/**
 * Profile page with Suspense boundary
 */
export default function ProfilePage() {
  return (
    <React.Suspense fallback={<ProfileLoading />}>
      <ProfilePageContent />
    </React.Suspense>
  );
}

/**
 * Metadata for the profile page
 */
export const metadata = {
  title: "Profile - Disc Golf Inventory",
  description: "Manage your account, preferences, and view your collection summary",
};
