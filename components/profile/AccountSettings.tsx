/**
 * Account Settings Component for Disc Golf Inventory Management System
 *
 * Provides data management options and account-related settings.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { UserProfile } from "@/lib/types";
import { ProfileOperationResult } from "@/hooks/useProfile";
import { useInventory } from "@/hooks/useInventory";
import { Download, Upload, RotateCcw, AlertTriangle, Database, Trash2, Shield } from "lucide-react";
// import { format } from "date-fns";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface AccountSettingsProps {
  profile: UserProfile;
  onResetProfile: () => Promise<ProfileOperationResult>;
  saving?: boolean;
}

// ============================================================================
// ACCOUNT SETTINGS COMPONENT
// ============================================================================

/**
 * Account settings component with data management options
 */
export function AccountSettings({ profile, onResetProfile, saving = false }: AccountSettingsProps) {
  const { discs } = useInventory();
  const [showResetDialog, setShowResetDialog] = React.useState(false);
  const [showClearDataDialog, setShowClearDataDialog] = React.useState(false);
  const [isExporting, setIsExporting] = React.useState(false);
  const [isImporting, setIsImporting] = React.useState(false);
  const [isClearing, setIsClearing] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState<string | null>(null);

  // File input ref for import
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  /**
   * Handle data export
   */
  const handleExport = async () => {
    try {
      setIsExporting(true);
      setError(null);
      setSuccess(null);

      // Create export data
      const exportData = {
        discs,
        profile,
        exportDate: new Date().toISOString(),
        version: "1.0.0",
      };

      // Create and download file
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `disc-golf-inventory-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      setSuccess("Data exported successfully");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Handle data import
   */
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsImporting(true);
      setError(null);
      setSuccess(null);

      const text = await file.text();
      const data = JSON.parse(text);

      // Basic validation
      if (!data.discs || !Array.isArray(data.discs)) {
        setError("Invalid file format: missing discs array");
        return;
      }

      setSuccess(`File loaded with ${data.discs.length} discs. Import functionality coming soon.`);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Invalid file format");
    } finally {
      setIsImporting(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  /**
   * Handle profile reset
   */
  const handleResetProfile = async () => {
    try {
      setError(null);
      setSuccess(null);

      const result = await onResetProfile();

      if (!result.success) {
        setError(result.error?.message || "Failed to reset profile");
        return;
      }

      setSuccess("Profile reset successfully");
      setShowResetDialog(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    }
  };

  /**
   * Handle clear all data
   */
  const handleClearAllData = async () => {
    try {
      setIsClearing(true);
      setError(null);
      setSuccess(null);

      // Clear localStorage
      localStorage.clear();

      setSuccess("All data cleared successfully");
      setShowClearDataDialog(false);

      // Reload page to reset state
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setIsClearing(false);
    }
  };

  /**
   * Clear messages after delay
   */
  React.useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  return (
    <div className="space-y-6">
      {/* Status Messages */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <Shield className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Data Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Export Data */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <div className="font-medium">Export Data</div>
              <div className="text-sm text-muted-foreground">Download your collection as a JSON file</div>
            </div>
            <Button variant="outline" onClick={handleExport} disabled={isExporting || discs.length === 0}>
              <Download className="h-4 w-4 mr-2" />
              {isExporting ? "Exporting..." : "Export"}
            </Button>
          </div>

          {/* Import Data */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <div className="font-medium">Import Data</div>
              <div className="text-sm text-muted-foreground">Upload a JSON file to restore your collection</div>
            </div>
            <div>
              <input ref={fileInputRef} type="file" accept=".json" onChange={handleImport} className="hidden" />
              <Button variant="outline" onClick={() => fileInputRef.current?.click()} disabled={isImporting}>
                <Upload className="h-4 w-4 mr-2" />
                {isImporting ? "Importing..." : "Import"}
              </Button>
            </div>
          </div>

          {/* Data Summary */}
          <div className="p-4 bg-muted/50 rounded-lg">
            <div className="text-sm font-medium mb-2">Current Data</div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Total Discs:</span>
                <span className="ml-2 font-medium">{discs.length}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Member Since:</span>
                <span className="ml-2 font-medium">
                  {profile.personalInfo.joinDate.toLocaleDateString("en-US", { month: "short", year: "numeric" })}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Account Actions</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Reset Profile */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <div className="font-medium">Reset Profile</div>
              <div className="text-sm text-muted-foreground">
                Reset preferences to default values (keeps collection data)
              </div>
            </div>
            <Dialog open={showResetDialog} onOpenChange={setShowResetDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" disabled={saving}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Reset Profile</DialogTitle>
                  <DialogDescription>
                    This will reset your profile preferences to default values. Your disc collection data will not be
                    affected.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowResetDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleResetProfile} disabled={saving}>
                    Reset Profile
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Clear All Data */}
          <div className="flex items-center justify-between p-4 border border-destructive/20 rounded-lg">
            <div>
              <div className="font-medium text-destructive">Clear All Data</div>
              <div className="text-sm text-muted-foreground">
                Permanently delete all collection data and profile information
              </div>
            </div>
            <Dialog open={showClearDataDialog} onOpenChange={setShowClearDataDialog}>
              <DialogTrigger asChild>
                <Button variant="destructive" disabled={isClearing}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Clear All Data</DialogTitle>
                  <DialogDescription>
                    This will permanently delete all your disc collection data and profile information. This action
                    cannot be undone. Consider exporting your data first.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowClearDataDialog(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleClearAllData} disabled={isClearing}>
                    {isClearing ? "Clearing..." : "Clear All Data"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
