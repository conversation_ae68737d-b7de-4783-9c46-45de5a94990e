/**
 * Profile Preferences Component for Disc Golf Inventory Management System
 *
 * Manages user preferences for collection display and app behavior.
 */

"use client";

import * as React from "react";
import { SettingsCard, SettingsItem, SettingsGroup } from "@/components/settings/SettingsCard";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UserCollectionPreferences } from "@/lib/types";
import { AppSettingsType } from "@/lib/validation";
import { ProfileOperationResult } from "@/hooks/useProfile";
import { Location } from "@/lib/types";
import { MapPin, Grid3X3, List, Palette, Monitor, Sun, Moon } from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface ProfilePreferencesProps {
  collectionPreferences: UserCollectionPreferences;
  appSettings: AppSettingsType;
  onUpdateCollectionPreferences: (updates: Partial<UserCollectionPreferences>) => Promise<ProfileOperationResult>;
  onUpdateAppSettings: (updates: Partial<AppSettingsType>) => Promise<ProfileOperationResult>;
  saving?: boolean;
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get location display name
 */
function getLocationDisplayName(location: Location): string {
  const locationNames = {
    [Location.BAG]: "In Bag",
    [Location.CAR]: "In Car",
    [Location.HOME]: "At Home",
    [Location.LOANED]: "Loaned Out",
    [Location.LOST]: "Lost",
  };
  return locationNames[location];
}

// /**
//  * Get theme icon
//  */
// function getThemeIcon(theme: string) {
//   switch (theme) {
//     case "light":
//       return Sun;
//     case "dark":
//       return Moon;
//     default:
//       return Monitor;
//   }
// }

// ============================================================================
// PROFILE PREFERENCES COMPONENT
// ============================================================================

/**
 * Profile preferences component with collection and app settings
 */
export function ProfilePreferences({
  collectionPreferences,
  appSettings,
  onUpdateCollectionPreferences,
  onUpdateAppSettings,
  saving = false,
}: ProfilePreferencesProps) {
  /**
   * Handle collection preference changes
   */
  const handleCollectionPreferenceChange = async (field: keyof UserCollectionPreferences, value: unknown) => {
    await onUpdateCollectionPreferences({ [field]: value });
  };

  /**
   * Handle app setting changes
   */
  const handleAppSettingChange = async (section: keyof AppSettingsType, field: string, value: unknown) => {
    await onUpdateAppSettings({
      [section]: {
        ...appSettings[section],
        [field]: value,
      },
    });
  };

  return (
    <div className="space-y-6">
      {/* Collection Preferences */}
      <SettingsCard
        title="Collection Preferences"
        description="Customize how your disc collection is managed"
        icon={MapPin}
        disabled={saving}
      >
        <SettingsGroup>
          <SettingsItem
            label="Default Location"
            description="Default location for new discs"
            control={
              <Select
                value={collectionPreferences.defaultLocation}
                onValueChange={(value) => handleCollectionPreferenceChange("defaultLocation", value as Location)}
                disabled={saving}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(Location).map((location) => (
                    <SelectItem key={location} value={location}>
                      {getLocationDisplayName(location)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            }
          />

          <SettingsItem
            label="Track Usage"
            description="Track when you view and edit discs"
            control={
              <Switch
                checked={collectionPreferences.trackUsage}
                onCheckedChange={(checked) => handleCollectionPreferenceChange("trackUsage", checked)}
                disabled={saving}
              />
            }
          />

          <SettingsItem
            label="Show Purchase Prices"
            description="Display purchase prices in collection views"
            control={
              <Switch
                checked={collectionPreferences.showPurchasePrices}
                onCheckedChange={(checked) => handleCollectionPreferenceChange("showPurchasePrices", checked)}
                disabled={saving}
              />
            }
          />

          <SettingsItem
            label="Show Condition Warnings"
            description="Highlight discs that need attention"
            control={
              <Switch
                checked={collectionPreferences.showConditionWarnings}
                onCheckedChange={(checked) => handleCollectionPreferenceChange("showConditionWarnings", checked)}
                disabled={saving}
              />
            }
          />
        </SettingsGroup>
      </SettingsCard>

      {/* Display Preferences */}
      <SettingsCard
        title="Display Preferences"
        description="Customize the appearance and layout"
        icon={Palette}
        disabled={saving}
      >
        <SettingsGroup>
          <SettingsItem
            label="Theme"
            description="Choose your preferred color scheme"
            control={
              <Select
                value={appSettings.appearance.theme}
                onValueChange={(value) => handleAppSettingChange("appearance", "theme", value)}
                disabled={saving}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">
                    <div className="flex items-center space-x-2">
                      <Sun className="h-4 w-4" />
                      <span>Light</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="dark">
                    <div className="flex items-center space-x-2">
                      <Moon className="h-4 w-4" />
                      <span>Dark</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="system">
                    <div className="flex items-center space-x-2">
                      <Monitor className="h-4 w-4" />
                      <span>System</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            }
          />

          <SettingsItem
            label="Default View"
            description="Default layout for disc collection"
            control={
              <Select
                value={appSettings.appearance.defaultView}
                onValueChange={(value) => handleAppSettingChange("appearance", "defaultView", value)}
                disabled={saving}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="grid">
                    <div className="flex items-center space-x-2">
                      <Grid3X3 className="h-4 w-4" />
                      <span>Grid</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="list">
                    <div className="flex items-center space-x-2">
                      <List className="h-4 w-4" />
                      <span>List</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            }
          />

          <SettingsItem
            label="Show Flight Numbers"
            description="Display flight numbers on disc cards"
            control={
              <Switch
                checked={appSettings.appearance.showFlightNumbers}
                onCheckedChange={(checked) => handleAppSettingChange("appearance", "showFlightNumbers", checked)}
                disabled={saving}
              />
            }
          />

          <SettingsItem
            label="Compact Mode"
            description="Use a more compact layout to fit more content"
            control={
              <Switch
                checked={appSettings.appearance.compactMode}
                onCheckedChange={(checked) => handleAppSettingChange("appearance", "compactMode", checked)}
                disabled={saving}
              />
            }
          />
        </SettingsGroup>
      </SettingsCard>

      {/* Data Preferences */}
      <SettingsCard title="Data Preferences" description="Manage data storage and backup settings" disabled={saving}>
        <SettingsGroup>
          <SettingsItem
            label="Auto Backup"
            description="Automatically backup your data"
            control={
              <Switch
                checked={appSettings.data.autoBackup}
                onCheckedChange={(checked) => handleAppSettingChange("data", "autoBackup", checked)}
                disabled={saving}
              />
            }
          />

          <SettingsItem
            label="Backup Frequency"
            description="How often to backup your data"
            control={
              <Select
                value={appSettings.data.backupFrequency}
                onValueChange={(value) => handleAppSettingChange("data", "backupFrequency", value)}
                disabled={saving || !appSettings.data.autoBackup}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            }
          />

          <SettingsItem
            label="Data Compression"
            description="Compress data to save storage space"
            control={
              <Switch
                checked={appSettings.data.compressionEnabled}
                onCheckedChange={(checked) => handleAppSettingChange("data", "compressionEnabled", checked)}
                disabled={saving}
              />
            }
          />
        </SettingsGroup>
      </SettingsCard>
    </div>
  );
}
