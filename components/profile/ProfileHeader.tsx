/**
 * Profile Header Component for Disc Golf Inventory Management System
 *
 * Displays user information and provides editing capabilities for personal details.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// import { Badge } from "@/components/ui/badge";
import { UserPersonalInfo, CollectionSummary } from "@/lib/types";
import { ProfileOperationResult } from "@/hooks/useProfile";
import { User, Edit2, Save, X, Calendar, Activity } from "lucide-react";
// import { formatDistanceToNow, format } from "date-fns";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface ProfileHeaderProps {
  personalInfo: UserPersonalInfo;
  collectionSummary: CollectionSummary;
  onUpdatePersonalInfo: (updates: Partial<UserPersonalInfo>) => Promise<ProfileOperationResult>;
  saving?: boolean;
}

// ============================================================================
// PROFILE HEADER COMPONENT
// ============================================================================

/**
 * Profile header component with user information and editing capabilities
 */
export function ProfileHeader({
  personalInfo,
  collectionSummary,
  onUpdatePersonalInfo,
  saving = false,
}: ProfileHeaderProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editForm, setEditForm] = React.useState({
    displayName: personalInfo.displayName || "",
    email: personalInfo.email || "",
  });
  const [localSaving, setLocalSaving] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  // Reset form when personalInfo changes
  React.useEffect(() => {
    setEditForm({
      displayName: personalInfo.displayName || "",
      email: personalInfo.email || "",
    });
  }, [personalInfo]);

  /**
   * Handle form input changes
   */
  const handleInputChange = (field: keyof typeof editForm, value: string) => {
    setEditForm((prev) => ({
      ...prev,
      [field]: value,
    }));
    setError(null);
  };

  /**
   * Handle save changes
   */
  const handleSave = async () => {
    try {
      setLocalSaving(true);
      setError(null);

      const updates: Partial<UserPersonalInfo> = {};

      // Only include changed fields
      if (editForm.displayName !== (personalInfo.displayName || "")) {
        updates.displayName = editForm.displayName.trim() || undefined;
      }

      if (editForm.email !== (personalInfo.email || "")) {
        updates.email = editForm.email.trim() || undefined;
      }

      // Only save if there are changes
      if (Object.keys(updates).length > 0) {
        const result = await onUpdatePersonalInfo(updates);

        if (!result.success) {
          setError(result.error?.message || "Failed to update profile");
          return;
        }
      }

      setIsEditing(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLocalSaving(false);
    }
  };

  /**
   * Handle cancel editing
   */
  const handleCancel = () => {
    setEditForm({
      displayName: personalInfo.displayName || "",
      email: personalInfo.email || "",
    });
    setError(null);
    setIsEditing(false);
  };

  /**
   * Format join date for display
   */
  const formatJoinDate = (date: Date) => {
    return `Joined ${date.toLocaleDateString("en-US", { month: "long", year: "numeric" })}`;
  };

  /**
   * Format last active date for display
   */
  const formatLastActive = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) return "Active just now";
    if (diffHours < 24) return `Active ${diffHours} hours ago`;
    if (diffDays === 1) return "Active yesterday";
    if (diffDays < 7) return `Active ${diffDays} days ago`;
    return `Active ${date.toLocaleDateString()}`;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Profile Information</span>
          </CardTitle>

          {!isEditing && (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)} disabled={saving}>
              <Edit2 className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* User Avatar and Basic Info */}
        <div className="flex items-start space-x-4">
          {/* Avatar */}
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-8 w-8 text-primary" />
          </div>

          {/* User Details */}
          <div className="flex-1 space-y-3">
            {isEditing ? (
              /* Edit Mode */
              <div className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="displayName">Display Name</Label>
                    <Input
                      id="displayName"
                      value={editForm.displayName}
                      onChange={(e) => handleInputChange("displayName", e.target.value)}
                      placeholder="Enter your display name"
                      disabled={localSaving}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={editForm.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      placeholder="Enter your email"
                      disabled={localSaving}
                    />
                  </div>
                </div>

                {/* Error Message */}
                {error && <div className="text-sm text-destructive">{error}</div>}

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <Button size="sm" onClick={handleSave} disabled={localSaving}>
                    <Save className="h-4 w-4 mr-2" />
                    {localSaving ? "Saving..." : "Save"}
                  </Button>

                  <Button variant="outline" size="sm" onClick={handleCancel} disabled={localSaving}>
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              /* Display Mode */
              <div className="space-y-3">
                {/* Display Name */}
                <div>
                  <h2 className="text-xl font-semibold">{personalInfo.displayName || "Disc Golf Player"}</h2>
                  {personalInfo.email && <p className="text-muted-foreground">{personalInfo.email}</p>}
                </div>

                {/* Metadata */}
                <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatJoinDate(personalInfo.joinDate)}</span>
                  </div>

                  <div className="flex items-center space-x-1">
                    <Activity className="h-4 w-4" />
                    <span>{formatLastActive(personalInfo.lastActiveDate)}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Collection Quick Stats */}
        {!isEditing && (
          <div className="border-t pt-4">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{collectionSummary.totalDiscs}</div>
                <div className="text-sm text-muted-foreground">Total Discs</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{collectionSummary.uniqueManufacturers}</div>
                <div className="text-sm text-muted-foreground">Manufacturers</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{collectionSummary.uniqueMolds}</div>
                <div className="text-sm text-muted-foreground">Unique Molds</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-primary">${collectionSummary.totalValue.toFixed(0)}</div>
                <div className="text-sm text-muted-foreground">Total Value</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
