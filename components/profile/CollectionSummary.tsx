/**
 * Collection Summary Component for Disc Golf Inventory Management System
 *
 * Displays comprehensive statistics and insights about the user's disc collection.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import type { CollectionSummary } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";
import {
  BarChart3,
  TrendingUp,
  Package,
  DollarSign,
  Star,
  Calendar,
  MapPin,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react";
// import { format } from "date-fns";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface CollectionSummaryProps {
  collectionSummary: CollectionSummary;
  showPurchasePrices?: boolean;
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Get condition color and icon
 */
function getConditionInfo(condition: DiscCondition) {
  const conditionMap = {
    [DiscCondition.NEW]: {
      color: "bg-green-500",
      label: "New",
      icon: Star,
      textColor: "text-green-600",
    },
    [DiscCondition.GOOD]: {
      color: "bg-blue-500",
      label: "Good",
      icon: CheckCircle,
      textColor: "text-blue-600",
    },
    [DiscCondition.FAIR]: {
      color: "bg-yellow-500",
      label: "Fair",
      icon: Clock,
      textColor: "text-yellow-600",
    },
    [DiscCondition.WORN]: {
      color: "bg-orange-500",
      label: "Worn",
      icon: AlertCircle,
      textColor: "text-orange-600",
    },
    [DiscCondition.DAMAGED]: {
      color: "bg-red-500",
      label: "Damaged",
      icon: AlertCircle,
      textColor: "text-red-600",
    },
  };
  return conditionMap[condition];
}

/**
 * Get location display info
 */
function getLocationInfo(location: Location) {
  const locationMap = {
    [Location.BAG]: { label: "In Bag", color: "bg-green-500", textColor: "text-green-600" },
    [Location.CAR]: { label: "In Car", color: "bg-blue-500", textColor: "text-blue-600" },
    [Location.HOME]: { label: "At Home", color: "bg-gray-500", textColor: "text-gray-600" },
    [Location.LOANED]: { label: "Loaned", color: "bg-yellow-500", textColor: "text-yellow-600" },
    [Location.LOST]: { label: "Lost", color: "bg-red-500", textColor: "text-red-600" },
  };
  return locationMap[location];
}

/**
 * Calculate percentage for distribution
 */
function calculatePercentage(value: number, total: number): number {
  return total > 0 ? Math.round((value / total) * 100) : 0;
}

// ============================================================================
// COLLECTION SUMMARY COMPONENT
// ============================================================================

/**
 * Collection summary component with statistics and insights
 */
export function CollectionSummary({ collectionSummary, showPurchasePrices = true }: CollectionSummaryProps) {
  const {
    totalDiscs,
    totalValue,
    uniqueManufacturers,
    uniqueMolds,
    newestDisc,
    mostValuableDisc,
    conditionDistribution,
    locationDistribution,
  } = collectionSummary;

  // Calculate condition health score
  const healthScore = React.useMemo(() => {
    if (totalDiscs === 0) return 0;

    const weights = {
      [DiscCondition.NEW]: 5,
      [DiscCondition.GOOD]: 4,
      [DiscCondition.FAIR]: 3,
      [DiscCondition.WORN]: 2,
      [DiscCondition.DAMAGED]: 1,
    };

    const totalScore = Object.entries(conditionDistribution).reduce(
      (sum, [condition, count]) => sum + weights[condition as DiscCondition] * count,
      0
    );

    const maxPossibleScore = totalDiscs * 5;
    return Math.round((totalScore / maxPossibleScore) * 100);
  }, [conditionDistribution, totalDiscs]);

  // Empty state
  if (totalDiscs === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Package className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Discs Yet</h3>
          <p className="text-muted-foreground text-center">Start building your collection to see statistics here</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Collection Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">{totalDiscs}</div>
              <div className="text-sm text-muted-foreground">Total Discs</div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-primary">{uniqueManufacturers}</div>
              <div className="text-sm text-muted-foreground">Manufacturers</div>
            </div>

            <div className="text-center">
              <div className="text-3xl font-bold text-primary">{uniqueMolds}</div>
              <div className="text-sm text-muted-foreground">Unique Molds</div>
            </div>

            {showPurchasePrices && (
              <div className="text-center">
                <div className="text-3xl font-bold text-primary">${totalValue.toFixed(0)}</div>
                <div className="text-sm text-muted-foreground">Total Value</div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Collection Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Collection Health</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Health Score</span>
              <Badge variant={healthScore >= 80 ? "default" : healthScore >= 60 ? "secondary" : "destructive"}>
                {healthScore}%
              </Badge>
            </div>
            <Progress value={healthScore} className="h-2" />

            <div className="grid grid-cols-1 gap-2">
              {Object.entries(conditionDistribution)
                .filter(([, count]) => count > 0)
                .map(([condition, count]) => {
                  const info = getConditionInfo(condition as DiscCondition);
                  const percentage = calculatePercentage(count, totalDiscs);

                  return (
                    <div key={condition} className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${info.color}`} />
                        <span>{info.label}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-muted-foreground">{count}</span>
                        <span className={`font-medium ${info.textColor}`}>{percentage}%</span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Location Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5" />
            <span>Storage Locations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Object.entries(locationDistribution)
              .filter(([, count]) => count > 0)
              .map(([location, count]) => {
                const info = getLocationInfo(location as Location);
                const percentage = calculatePercentage(count, totalDiscs);

                return (
                  <div key={location} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${info.color}`} />
                      <span className="text-sm">{info.label}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">{count}</span>
                      <span className={`text-sm font-medium ${info.textColor}`}>{percentage}%</span>
                    </div>
                  </div>
                );
              })}
          </div>
        </CardContent>
      </Card>

      {/* Notable Discs */}
      {(newestDisc || mostValuableDisc) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Star className="h-5 w-5" />
              <span>Notable Discs</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {newestDisc && (
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div>
                  <div className="font-medium">Newest Addition</div>
                  <div className="text-sm text-muted-foreground">
                    {newestDisc.manufacturer} {newestDisc.mold}
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>{newestDisc.createdAt.toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            )}

            {mostValuableDisc && showPurchasePrices && mostValuableDisc.purchasePrice && (
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div>
                  <div className="font-medium">Most Valuable</div>
                  <div className="text-sm text-muted-foreground">
                    {mostValuableDisc.manufacturer} {mostValuableDisc.mold}
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-1 text-sm font-medium">
                    <DollarSign className="h-4 w-4" />
                    <span>${mostValuableDisc.purchasePrice.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
