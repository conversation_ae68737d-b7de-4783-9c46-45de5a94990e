/**
 * Header Component for the Disc Golf Inventory Management System
 *
 * This component provides the main navigation header with responsive design,
 * mobile-friendly hamburger menu, and proper semantic HTML structure.
 */

"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Menu, X, Search, Plus, Settings, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Navigation item interface
 */
interface NavItem {
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  description?: string;
}

/**
 * Header component props
 */
interface HeaderProps {
  /** Additional CSS classes */
  className?: string;
  /** Whether to show search functionality */
  showSearch?: boolean;
  /** Custom navigation items */
  navItems?: NavItem[];
  /** Current active path */
  currentPath?: string;
}

// ============================================================================
// NAVIGATION DATA
// ============================================================================

/**
 * Default navigation items for the disc golf inventory app
 */
const defaultNavItems: NavItem[] = [
  {
    label: "Inventory",
    href: "/inventory",
    icon: Search,
    description: "View and manage your disc collection",
  },
  {
    label: "Add Disc",
    href: "/add-disc",
    icon: Plus,
    description: "Add a new disc to your collection",
  },
  {
    label: "Statistics",
    href: "/stats",
    icon: Settings,
    description: "View collection statistics and insights",
  },
];

// ============================================================================
// HEADER COMPONENT
// ============================================================================

/**
 * Main header component with responsive navigation
 */
export function Header({ className, showSearch = true, navItems = defaultNavItems, currentPath = "/" }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  /**
   * Toggle mobile menu visibility
   */
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  /**
   * Close mobile menu
   */
  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  /**
   * Handle search input change
   */
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  /**
   * Handle search submission
   */
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // TODO: Implement search functionality
      console.log("Search query:", searchQuery);
    }
  };

  /**
   * Check if a navigation item is active
   */
  const isActiveNavItem = (href: string) => {
    return currentPath === href || currentPath.startsWith(href + "/");
  };

  return (
    <header
      className={cn(
        "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        className
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center space-x-2 text-xl font-bold text-foreground hover:text-primary transition-colors"
              onClick={closeMobileMenu}
            >
              <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">DG</span>
              </div>
              <span className="hidden sm:inline-block">Disc Golf Inventory</span>
              <span className="sm:hidden">DGI</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActiveNavItem(item.href);

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-accent"
                  )}
                  title={item.description}
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>

          {/* Search Bar (Desktop) */}
          {showSearch && (
            <div className="hidden lg:flex items-center space-x-4">
              <form onSubmit={handleSearchSubmit} className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search discs..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="pl-10 w-64"
                />
              </form>
            </div>
          )}

          {/* User Actions */}
          <div className="flex items-center space-x-2">
            {/* User Profile Button */}
            <Button
              variant="ghost"
              size="sm"
              className="hidden sm:flex items-center space-x-2"
              title="User Profile"
              asChild
            >
              <Link href="/profile">
                <User className="h-4 w-4" />
                <span className="hidden lg:inline">Profile</span>
              </Link>
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={toggleMobileMenu}
              aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
              aria-expanded={isMobileMenuOpen}
            >
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Search Bar */}
        {showSearch && (
          <div className="lg:hidden py-3 border-t">
            <form onSubmit={handleSearchSubmit} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search discs..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 w-full"
              />
            </form>
          </div>
        )}

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t bg-background">
            <nav className="py-4 space-y-2">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = isActiveNavItem(item.href);

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center space-x-3 px-4 py-3 text-base font-medium transition-colors",
                      isActive ? "bg-primary text-primary-foreground" : "text-foreground hover:bg-accent"
                    )}
                    onClick={closeMobileMenu}
                  >
                    {Icon && <Icon className="h-5 w-5" />}
                    <div>
                      <div>{item.label}</div>
                      {item.description && <div className="text-sm text-muted-foreground mt-1">{item.description}</div>}
                    </div>
                  </Link>
                );
              })}

              {/* Mobile User Profile */}
              <div className="border-t pt-4 mt-4">
                <Link
                  href="/profile"
                  className="flex items-center space-x-3 px-4 py-3 text-base font-medium text-foreground hover:bg-accent transition-colors"
                  onClick={closeMobileMenu}
                >
                  <User className="h-5 w-5" />
                  <div>
                    <div>Profile</div>
                    <div className="text-sm text-muted-foreground mt-1">Manage your account settings</div>
                  </div>
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}

// ============================================================================
// EXPORTS
// ============================================================================

export default Header;
