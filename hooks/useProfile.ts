/**
 * useProfile Hook for the Disc Golf Inventory Management System
 *
 * This hook provides comprehensive user profile management including personal information,
 * collection preferences, app settings, and collection summary statistics.
 */

"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { useInventory } from "@/hooks/useInventory";
import { STORAGE_KEYS } from "@/lib/storage";
import { calculateCollectionSummary } from "@/lib/discUtils";
import { validateUserProfile, AppSettingsType } from "@/lib/validation";
import { UserProfile, UserPersonalInfo, UserCollectionPreferences, CollectionSummary, Location } from "@/lib/types";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Default user profile configuration
 */
const DEFAULT_PERSONAL_INFO: UserPersonalInfo = {
  joinDate: new Date(),
  lastActiveDate: new Date(),
};

const DEFAULT_COLLECTION_PREFERENCES: UserCollectionPreferences = {
  defaultLocation: Location.HOME,
  trackUsage: true,
  showPurchasePrices: true,
  showConditionWarnings: true,
};

const DEFAULT_APP_SETTINGS: AppSettingsType = {
  appearance: {
    theme: "system",
    compactMode: false,
    showFlightNumbers: true,
    defaultView: "grid",
  },
  data: {
    autoBackup: false,
    backupFrequency: "weekly",
    maxStorageSize: 50,
    compressionEnabled: true,
  },
  advanced: {
    debugMode: false,
    performanceMode: false,
    experimentalFeatures: false,
  },
};

const DEFAULT_PROFILE: UserProfile = {
  personalInfo: DEFAULT_PERSONAL_INFO,
  collectionPreferences: DEFAULT_COLLECTION_PREFERENCES,
  appSettings: DEFAULT_APP_SETTINGS,
};

/**
 * Profile operation result type
 */
export interface ProfileOperationResult<T = void> {
  success: boolean;
  data?: T;
  error?: {
    type: string;
    message: string;
    details?: unknown;
  };
}

/**
 * Hook return type
 */
export interface UseProfileReturn {
  // Profile data
  profile: UserProfile;
  collectionSummary: CollectionSummary;

  // Loading states
  loading: boolean;
  saving: boolean;

  // Error states
  error: string | null;

  // Profile management methods
  updatePersonalInfo: (updates: Partial<UserPersonalInfo>) => Promise<ProfileOperationResult>;
  updateCollectionPreferences: (updates: Partial<UserCollectionPreferences>) => Promise<ProfileOperationResult>;
  updateAppSettings: (updates: Partial<AppSettingsType>) => Promise<ProfileOperationResult>;
  resetProfile: () => Promise<ProfileOperationResult>;

  // Utility methods
  refreshCollectionSummary: () => void;
  updateLastActiveDate: () => void;
}

// ============================================================================
// HOOK IMPLEMENTATION
// ============================================================================

/**
 * Custom hook for user profile management
 *
 * Features:
 * - Complete profile data management with localStorage persistence
 * - Real-time collection summary calculation
 * - Integration with existing settings and inventory systems
 * - Validation and error handling
 * - Cross-tab synchronization
 *
 * @returns Hook state and methods
 */
export function useProfile(): UseProfileReturn {
  // Profile data with localStorage persistence
  const {
    value: profile = DEFAULT_PROFILE,
    setValue: setProfile,
    loading: storageLoading,
    error: storageError,
  } = useLocalStorage<UserProfile>(STORAGE_KEYS.USER_PROFILE, {
    defaultValue: DEFAULT_PROFILE,
    syncAcrossTabs: true,
  });

  // Inventory data for collection summary
  const { discs, loading: inventoryLoading } = useInventory();

  // Local state
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Combine loading states
  const loading = storageLoading || inventoryLoading;

  // Calculate collection summary
  const collectionSummary = useMemo(() => {
    return calculateCollectionSummary(discs);
  }, [discs]);

  // Handle storage errors
  useEffect(() => {
    if (storageError) {
      setError(storageError.message || "Failed to load profile data");
    } else {
      setError(null);
    }
  }, [storageError]);

  // Update last active date on mount and periodically
  useEffect(() => {
    const updateLastActive = () => {
      setProfile((prev) => ({
        ...(prev || DEFAULT_PROFILE),
        personalInfo: {
          ...(prev?.personalInfo || DEFAULT_PERSONAL_INFO),
          lastActiveDate: new Date(),
        },
      }));
    };

    updateLastActive();

    // Update every 5 minutes while the app is active
    const interval = setInterval(updateLastActive, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [setProfile]);

  /**
   * Update personal information
   */
  const updatePersonalInfo = useCallback(
    async (updates: Partial<UserPersonalInfo>): Promise<ProfileOperationResult> => {
      try {
        setSaving(true);
        setError(null);

        const updatedProfile = {
          ...profile,
          personalInfo: {
            ...profile.personalInfo,
            ...updates,
          },
        };

        // Validate the updated profile
        const validation = validateUserProfile(updatedProfile);
        if (!validation.isValid) {
          const errorMessage = validation.errors.map((err) => err.message).join(", ");
          setError(errorMessage);
          return {
            success: false,
            error: {
              type: "VALIDATION_ERROR",
              message: errorMessage,
              details: validation.errors,
            },
          };
        }

        setProfile(updatedProfile);
        return { success: true };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
        setError(errorMessage);
        return {
          success: false,
          error: {
            type: "OPERATION_FAILED",
            message: errorMessage,
            details: err,
          },
        };
      } finally {
        setSaving(false);
      }
    },
    [profile, setProfile]
  );

  /**
   * Update collection preferences
   */
  const updateCollectionPreferences = useCallback(
    async (updates: Partial<UserCollectionPreferences>): Promise<ProfileOperationResult> => {
      try {
        setSaving(true);
        setError(null);

        const updatedProfile = {
          ...profile,
          collectionPreferences: {
            ...profile.collectionPreferences,
            ...updates,
          },
        };

        // Validate the updated profile
        const validation = validateUserProfile(updatedProfile);
        if (!validation.isValid) {
          const errorMessage = validation.errors.map((err) => err.message).join(", ");
          setError(errorMessage);
          return {
            success: false,
            error: {
              type: "VALIDATION_ERROR",
              message: errorMessage,
              details: validation.errors,
            },
          };
        }

        setProfile(updatedProfile);
        return { success: true };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
        setError(errorMessage);
        return {
          success: false,
          error: {
            type: "OPERATION_FAILED",
            message: errorMessage,
            details: err,
          },
        };
      } finally {
        setSaving(false);
      }
    },
    [profile, setProfile]
  );

  /**
   * Update app settings
   */
  const updateAppSettings = useCallback(
    async (updates: Partial<AppSettingsType>): Promise<ProfileOperationResult> => {
      try {
        setSaving(true);
        setError(null);

        const updatedProfile = {
          ...profile,
          appSettings: {
            ...profile.appSettings,
            ...updates,
          },
        };

        // Validate the updated profile
        const validation = validateUserProfile(updatedProfile);
        if (!validation.isValid) {
          const errorMessage = validation.errors.map((err) => err.message).join(", ");
          setError(errorMessage);
          return {
            success: false,
            error: {
              type: "VALIDATION_ERROR",
              message: errorMessage,
              details: validation.errors,
            },
          };
        }

        setProfile(updatedProfile);
        return { success: true };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
        setError(errorMessage);
        return {
          success: false,
          error: {
            type: "OPERATION_FAILED",
            message: errorMessage,
            details: err,
          },
        };
      } finally {
        setSaving(false);
      }
    },
    [profile, setProfile]
  );

  /**
   * Reset profile to defaults
   */
  const resetProfile = useCallback(async (): Promise<ProfileOperationResult> => {
    try {
      setSaving(true);
      setError(null);

      const resetProfile = {
        ...DEFAULT_PROFILE,
        personalInfo: {
          ...DEFAULT_PROFILE.personalInfo,
          joinDate: profile.personalInfo.joinDate, // Preserve join date
        },
      };

      setProfile(resetProfile);
      return { success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      return {
        success: false,
        error: {
          type: "OPERATION_FAILED",
          message: errorMessage,
          details: err,
        },
      };
    } finally {
      setSaving(false);
    }
  }, [profile.personalInfo.joinDate, setProfile]);

  /**
   * Refresh collection summary (force recalculation)
   */
  const refreshCollectionSummary = useCallback(() => {
    // Collection summary is automatically recalculated via useMemo
    // This function exists for API consistency and future extensibility
  }, []);

  /**
   * Update last active date manually
   */
  const updateLastActiveDate = useCallback(() => {
    setProfile((prev) => ({
      ...(prev || DEFAULT_PROFILE),
      personalInfo: {
        ...(prev?.personalInfo || DEFAULT_PERSONAL_INFO),
        lastActiveDate: new Date(),
      },
    }));
  }, [setProfile]);

  return {
    // Profile data
    profile,
    collectionSummary,

    // Loading states
    loading,
    saving,

    // Error states
    error,

    // Profile management methods
    updatePersonalInfo,
    updateCollectionPreferences,
    updateAppSettings,
    resetProfile,

    // Utility methods
    refreshCollectionSummary,
    updateLastActiveDate,
  };
}
