/**
 * Data validation schemas using Zod for the Disc Golf Inventory Management System
 *
 * This file contains all validation schemas and utility functions for validating
 * disc golf inventory data according to disc golf standards and business rules.
 */

import { z } from "zod";
import {
  DiscCondition,
  Location,
  FLIGHT_NUMBER_RANGES,
  DISC_WEIGHT_RANGE,
  CHARACTER_LIMITS,
  type ValidationResult,
  type ValidationError,
} from "./types";

// ============================================================================
// CORE VALIDATION SCHEMAS
// ============================================================================

/**
 * Flight numbers validation schema
 * Validates disc flight characteristics according to disc golf standards
 */
export const FlightNumbersSchema = z.object({
  speed: z
    .number()
    .min(
      FLIGHT_NUMBER_RANGES.speed.min,
      `Speed must be between ${FLIGHT_NUMBER_RANGES.speed.min} and ${FLIGHT_NUMBER_RANGES.speed.max}`
    )
    .max(
      FLIGHT_NUMBER_RANGES.speed.max,
      `Speed must be between ${FLIGHT_NUMBER_RANGES.speed.min} and ${FLIGHT_NUMBER_RANGES.speed.max}`
    )
    .int("Speed must be a whole number"),

  glide: z
    .number()
    .min(
      FLIGHT_NUMBER_RANGES.glide.min,
      `Glide must be between ${FLIGHT_NUMBER_RANGES.glide.min} and ${FLIGHT_NUMBER_RANGES.glide.max}`
    )
    .max(
      FLIGHT_NUMBER_RANGES.glide.max,
      `Glide must be between ${FLIGHT_NUMBER_RANGES.glide.min} and ${FLIGHT_NUMBER_RANGES.glide.max}`
    )
    .int("Glide must be a whole number"),

  turn: z
    .number()
    .min(
      FLIGHT_NUMBER_RANGES.turn.min,
      `Turn must be between ${FLIGHT_NUMBER_RANGES.turn.min} and ${FLIGHT_NUMBER_RANGES.turn.max}`
    )
    .max(
      FLIGHT_NUMBER_RANGES.turn.max,
      `Turn must be between ${FLIGHT_NUMBER_RANGES.turn.min} and ${FLIGHT_NUMBER_RANGES.turn.max}`
    )
    .int("Turn must be a whole number"),

  fade: z
    .number()
    .min(
      FLIGHT_NUMBER_RANGES.fade.min,
      `Fade must be between ${FLIGHT_NUMBER_RANGES.fade.min} and ${FLIGHT_NUMBER_RANGES.fade.max}`
    )
    .max(
      FLIGHT_NUMBER_RANGES.fade.max,
      `Fade must be between ${FLIGHT_NUMBER_RANGES.fade.min} and ${FLIGHT_NUMBER_RANGES.fade.max}`
    )
    .int("Fade must be a whole number"),
});

/**
 * Complete disc validation schema
 * Validates all disc properties according to business rules
 */
export const DiscSchema = z.object({
  id: z.string().uuid("Invalid disc ID format"),

  manufacturer: z
    .string()
    .min(1, "Manufacturer is required")
    .max(CHARACTER_LIMITS.manufacturer, `Manufacturer must be ${CHARACTER_LIMITS.manufacturer} characters or less`)
    .trim(),

  mold: z
    .string()
    .min(1, "Mold is required")
    .max(CHARACTER_LIMITS.mold, `Mold must be ${CHARACTER_LIMITS.mold} characters or less`)
    .trim(),

  plasticType: z
    .string()
    .min(1, "Plastic type is required")
    .max(CHARACTER_LIMITS.plasticType, `Plastic type must be ${CHARACTER_LIMITS.plasticType} characters or less`)
    .trim(),

  weight: z
    .number()
    .min(DISC_WEIGHT_RANGE.min, `Weight must be between ${DISC_WEIGHT_RANGE.min} and ${DISC_WEIGHT_RANGE.max} grams`)
    .max(DISC_WEIGHT_RANGE.max, `Weight must be between ${DISC_WEIGHT_RANGE.min} and ${DISC_WEIGHT_RANGE.max} grams`)
    .int("Weight must be a whole number"),

  condition: z.nativeEnum(DiscCondition, {
    message: "Invalid disc condition",
  }),

  flightNumbers: FlightNumbersSchema,

  color: z
    .string()
    .min(1, "Color is required")
    .max(CHARACTER_LIMITS.color, `Color must be ${CHARACTER_LIMITS.color} characters or less`)
    .trim(),

  notes: z
    .string()
    .max(CHARACTER_LIMITS.notes, `Notes must be ${CHARACTER_LIMITS.notes} characters or less`)
    .optional()
    .transform((val) => val?.trim() || undefined),

  purchaseDate: z.date().max(new Date(), "Purchase date cannot be in the future").optional(),

  purchasePrice: z
    .number()
    .positive("Purchase price must be positive")
    .max(1000, "Purchase price seems unreasonably high")
    .optional(),

  currentLocation: z.nativeEnum(Location, {
    message: "Invalid location",
  }),

  imageUrl: z.string().url("Invalid image URL format").optional().or(z.literal("")),

  createdAt: z.date(),

  updatedAt: z.date(),
});

/**
 * Schema for creating a new disc (without auto-generated fields)
 */
export const CreateDiscSchema = DiscSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

/**
 * Schema for updating an existing disc
 */
export const UpdateDiscSchema = DiscSchema.partial().extend({
  id: z.string().uuid("Invalid disc ID format"),
  updatedAt: z.date(),
});

/**
 * Schema for disc filter criteria
 */
export const DiscFilterSchema = z
  .object({
    manufacturer: z.string().optional(),
    mold: z.string().optional(),
    plasticType: z.string().optional(),
    condition: z.nativeEnum(DiscCondition).optional(),
    location: z.nativeEnum(Location).optional(),
    minWeight: z.number().min(DISC_WEIGHT_RANGE.min).optional(),
    maxWeight: z.number().max(DISC_WEIGHT_RANGE.max).optional(),
    minSpeed: z.number().min(FLIGHT_NUMBER_RANGES.speed.min).optional(),
    maxSpeed: z.number().max(FLIGHT_NUMBER_RANGES.speed.max).optional(),
    color: z.string().optional(),
    searchTerm: z.string().optional(),
  })
  .refine((data) => !data.minWeight || !data.maxWeight || data.minWeight <= data.maxWeight, {
    message: "Minimum weight must be less than or equal to maximum weight",
    path: ["minWeight"],
  })
  .refine((data) => !data.minSpeed || !data.maxSpeed || data.minSpeed <= data.maxSpeed, {
    message: "Minimum speed must be less than or equal to maximum speed",
    path: ["minSpeed"],
  });

// ============================================================================
// VALIDATION UTILITY FUNCTIONS
// ============================================================================

/**
 * Validates disc data and returns a structured result
 * @param disc - The disc object to validate
 * @param strict - Whether to apply strict validation rules
 * @returns Validation result with errors if any
 * @example
 * const result = validateDisc(discData);
 * if (!result.isValid) {
 *   console.log(result.errors);
 * }
 */
export function validateDisc(disc: unknown, strict = false): ValidationResult {
  try {
    const schema = strict ? DiscSchema.strict() : DiscSchema;
    schema.parse(disc);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: ValidationError[] = error.issues.map((err: z.ZodIssue) => ({
        field: err.path.join("."),
        message: err.message,
        code: err.code,
      }));
      return { isValid: false, errors };
    }
    return {
      isValid: false,
      errors: [{ field: "unknown", message: "Unknown validation error", code: "unknown" }],
    };
  }
}

/**
 * Validates flight numbers specifically
 * @param flightNumbers - The flight numbers to validate
 * @returns Validation result with errors if any
 */
export function validateFlightNumbers(flightNumbers: unknown): ValidationResult {
  try {
    FlightNumbersSchema.parse(flightNumbers);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: ValidationError[] = error.issues.map((err: z.ZodIssue) => ({
        field: err.path.join("."),
        message: err.message,
        code: err.code,
      }));
      return { isValid: false, errors };
    }
    return {
      isValid: false,
      errors: [{ field: "unknown", message: "Unknown validation error", code: "unknown" }],
    };
  }
}

/**
 * Validates disc creation input
 * @param input - The disc creation input to validate
 * @returns Validation result with errors if any
 */
export function validateCreateDiscInput(input: unknown): ValidationResult {
  try {
    CreateDiscSchema.parse(input);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: ValidationError[] = error.issues.map((err: z.ZodIssue) => ({
        field: err.path.join("."),
        message: err.message,
        code: err.code,
      }));
      return { isValid: false, errors };
    }
    return {
      isValid: false,
      errors: [{ field: "unknown", message: "Unknown validation error", code: "unknown" }],
    };
  }
}

/**
 * Validates disc filter criteria
 * @param filters - The filter criteria to validate
 * @returns Validation result with errors if any
 */
export function validateDiscFilters(filters: unknown): ValidationResult {
  try {
    DiscFilterSchema.parse(filters);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: ValidationError[] = error.issues.map((err: z.ZodIssue) => ({
        field: err.path.join("."),
        message: err.message,
        code: err.code,
      }));
      return { isValid: false, errors };
    }
    return {
      isValid: false,
      errors: [{ field: "unknown", message: "Unknown validation error", code: "unknown" }],
    };
  }
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// Export inferred types from schemas for use throughout the application
export type DiscSchemaType = z.infer<typeof DiscSchema>;
export type CreateDiscSchemaType = z.infer<typeof CreateDiscSchema>;
export type UpdateDiscSchemaType = z.infer<typeof UpdateDiscSchema>;
export type FlightNumbersSchemaType = z.infer<typeof FlightNumbersSchema>;
export type DiscFilterSchemaType = z.infer<typeof DiscFilterSchema>;

// ============================================================================
// SETTINGS VALIDATION SCHEMAS
// ============================================================================

/**
 * App settings validation schema
 */
export const AppSettingsSchema = z.object({
  appearance: z.object({
    theme: z.enum(["light", "dark", "system"], {
      message: "Theme must be light, dark, or system",
    }),
    compactMode: z.boolean(),
    showFlightNumbers: z.boolean(),
    defaultView: z.enum(["grid", "list"], {
      message: "Default view must be grid or list",
    }),
  }),
  data: z.object({
    autoBackup: z.boolean(),
    backupFrequency: z.enum(["daily", "weekly", "monthly"], {
      message: "Backup frequency must be daily, weekly, or monthly",
    }),
    maxStorageSize: z.number().min(1).max(100),
    compressionEnabled: z.boolean(),
  }),
  advanced: z.object({
    debugMode: z.boolean(),
    performanceMode: z.boolean(),
    experimentalFeatures: z.boolean(),
  }),
});

/**
 * Settings schema type
 */
export type AppSettingsType = z.infer<typeof AppSettingsSchema>;

/**
 * Validate app settings
 */
export function validateAppSettings(data: unknown): ValidationResult {
  try {
    AppSettingsSchema.parse(data);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.issues.map((err: any) => ({
          field: err.path.join("."),
          message: err.message,
          code: err.code,
        })),
      };
    }
    return {
      isValid: false,
      errors: [{ field: "unknown", message: "Unknown validation error", code: "unknown" }],
    };
  }
}

// ============================================================================
// USER PROFILE VALIDATION SCHEMAS
// ============================================================================

/**
 * User personal information validation schema
 */
export const UserPersonalInfoSchema = z.object({
  displayName: z
    .string()
    .max(CHARACTER_LIMITS.displayName, `Display name must be ${CHARACTER_LIMITS.displayName} characters or less`)
    .trim()
    .optional(),
  email: z
    .string()
    .email("Invalid email format")
    .max(CHARACTER_LIMITS.email, `Email must be ${CHARACTER_LIMITS.email} characters or less`)
    .trim()
    .optional(),
  joinDate: z.date(),
  lastActiveDate: z.date(),
});

/**
 * User collection preferences validation schema
 */
export const UserCollectionPreferencesSchema = z.object({
  defaultLocation: z.nativeEnum(Location, {
    message: "Invalid default location",
  }),
  trackUsage: z.boolean(),
  showPurchasePrices: z.boolean(),
  showConditionWarnings: z.boolean(),
});

/**
 * Complete user profile validation schema
 */
export const UserProfileSchema = z.object({
  personalInfo: UserPersonalInfoSchema,
  collectionPreferences: UserCollectionPreferencesSchema,
  appSettings: AppSettingsSchema,
});

/**
 * User profile schema types
 */
export type UserPersonalInfoType = z.infer<typeof UserPersonalInfoSchema>;
export type UserCollectionPreferencesType = z.infer<typeof UserCollectionPreferencesSchema>;
export type UserProfileType = z.infer<typeof UserProfileSchema>;

/**
 * Validate user profile data
 */
export function validateUserProfile(data: unknown): ValidationResult {
  try {
    UserProfileSchema.parse(data);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.issues.map((err: any) => ({
          field: err.path.join("."),
          message: err.message,
          code: err.code,
        })),
      };
    }
    return {
      isValid: false,
      errors: [{ field: "unknown", message: "Unknown validation error", code: "unknown" }],
    };
  }
}
